'use client';

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import CommissionerProfileView from '../../../../../components/user-profiles/recruiter/commissioner-profile-view';
import { PageSkeleton } from '../../../../../components/ui/loading-skeleton';
import ProfileHeader from '../../../../../components/user-profiles/profile-header';
import ProfileInfo from '../../../../../components/user-profiles/profile-info';
import WorkSamples from '../../../../../components/user-profiles/work-samples';

interface WorkSample {
  id: string;
  userId: number;
  title: string;
  image: string;
  skill: string;
  tool: string;
  year: number;
  url: string;
}

interface Tool {
  name: string;
  icon: string | null;
}

interface SocialLink {
  platform: string;
  url: string;
  icon: string;
}

interface Profile {
  id: number;
  name: string;
  title: string;
  avatar: string;
  type: 'freelancer' | 'commissioner';
  location?: string;
  rate?: string;
  availability?: string;
  hourlyRate?: {
    min: number;
    max: number;
  };
  rating?: number;
  about: string;
  skills?: string[];
  tools?: Tool[];
  socialLinks?: SocialLink[];
  workSamples?: WorkSample[];
  responsibilities?: string[];
}

export default function CommissionerProfilePage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accessDenied, setAccessDenied] = useState(false);

  const userId = params?.id as string;
  const currentUserId = session?.user?.id;
  const isOwnProfile = userId === currentUserId;
  const currentUserType = (session?.user as any)?.userType;

  // Session validation - prevent auto-logout for cross-user-type profile viewing
  useEffect(() => {
    if (status === 'loading') return; // Still loading session

    if (!session?.user?.id) {
      // No session - redirect to appropriate login page
      router.push('/login-commissioner');
      return;
    }

    // Allow commissioners to view any profile without auto-logout
    // Only restrict access if session is invalid, not based on user type
    if (session?.user?.id) {
      setAccessDenied(false);
    }
  }, [session, status, router]);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch(`/api/user/profile/${userId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch profile');
        }

        const data = await response.json();
        setProfile(data);
      } catch (err) {
        console.error('Error fetching profile:', err);
        setError(err instanceof Error ? err.message : 'Failed to load profile');
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchProfile();
    }
  }, [userId]);

  // Handle session loading
  if (status === 'loading') {
    return <PageSkeleton />;
  }

  // Handle access denied (will redirect, but show loading in the meantime)
  if (accessDenied || (!session?.user?.id)) {
    return <PageSkeleton />;
  }

  if (loading) {
    return <PageSkeleton />;
  }

  if (error || !profile) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            {error || 'Profile not found'}
          </h2>
        </div>
      </div>
    );
  }

  // If viewing a commissioner profile, use the commissioner profile view
  if (profile.type === 'commissioner') {
    return (
      <CommissionerProfileView
        userId={userId}
        isOwnProfile={isOwnProfile}
        viewerUserType="commissioner"
        profileType="commissioner"
      />
    );
  }

  // If viewing a freelancer profile as a commissioner, show read-only freelancer profile
  const profileHeaderData = profile ? {
    id: profile.id.toString(),
    name: profile.name,
    avatar: profile.avatar,
    location: profile.location || '',
    rate: profile.rate,
    rating: profile.rating,
    socialLinks: profile.socialLinks || []
  } : null;

  const workSamplesData = profile?.workSamples?.map(sample => ({
    id: sample.id,
    title: sample.title,
    coverImage: sample.image,
    link: sample.url,
    skills: [sample.skill],
    tools: [sample.tool],
    year: sample.year
  })) || [];

  return (
    <div className="flex-1 p-6 bg-white min-h-screen font-['Plus_Jakarta_Sans']">
      {/* Two Column Layout with better proportions */}
      <div className="grid grid-cols-1 lg:grid-cols-[1fr_500px] gap-8 h-full">
        {/* Left Column: User Info */}
        <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100">
          {profileHeaderData && (
            <ProfileHeader
              profile={profileHeaderData}
              isOwnProfile={false} // Always false for commissioners viewing freelancer profiles
              viewerUserType="commissioner"
              profileType={profile.type}
            />
          )}

          <ProfileInfo
            bio={profile.about}
            skills={profile.skills || []}
            tools={profile.tools?.map(t => t.name) || []}
            responsibilities={profile.responsibilities}
            isOwnProfile={false} // Always false for commissioners viewing freelancer profiles - no edit controls
            userType={profile.type}
            availableSkills={[]}
            availableTools={[]}
            onAddSkillTool={undefined} // No add functionality for commissioners
            onRemoveSkillTool={undefined} // No remove functionality for commissioners
          />
        </div>

        {/* Right Column: Work Samples */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <WorkSamples
            workSamples={workSamplesData}
            isOwnProfile={false} // Always false for commissioners viewing freelancer profiles - no add button
            onAddWorkSample={() => {}} // No add functionality for commissioners
          />
        </div>
      </div>
    </div>
  );
}